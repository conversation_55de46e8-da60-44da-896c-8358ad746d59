export default {
  // Common
  common: {
    search: 'Search',
    reset: 'Reset',
    create: 'Create',
    edit: 'Edit',
    delete: 'Delete',
    save: 'Save',
    cancel: 'Cancel',
    confirm: 'Confirm',
    back: 'Back',
    actions: 'Actions',
    status: 'Status',
    createdAt: 'Created At',
    updatedAt: 'Updated At',
    publishedAt: 'Published At',
  },

  // Benefits Boards
  boards: {
    title: 'Benefits Boards',
    name: 'Board Name',
    description: 'Description',
    type: 'Type',
    typePublic: 'Public',
    typePrivate: 'Private',
    blocked: 'Blocked',
    isDefault: 'Default Board',
    order: 'Order',
    postingPermission: 'Posting Permission',
    userLevelLimited: 'User Level Limited',
    related: 'Related',
    creator: 'Creator',
    subscribers: 'Subscribers',
    couponsCount: 'Coupons Count',
    
    // Locales
    locales: 'Localized Names',
    localesEn: 'English Name',
    localesKo: 'Korean Name',
    localesZhCN: 'Chinese Name',
    
    // Messages
    createSuccess: 'Board created successfully',
    updateSuccess: 'Board updated successfully',
    deleteSuccess: 'Board deleted successfully',
    deleteConfirm: 'Are you sure you want to delete this board?',
    
    // Form validation
    nameRequired: 'Board name is required',
    typeRequired: 'Board type is required',
    orderMustBeNumber: 'Order must be a number',
    postingPermissionMustBeNumber: 'Posting permission must be a number',
  },

  // Benefits Coupons
  coupons: {
    title: 'Benefits Coupons',
    couponTitle: 'Coupon Title',
    content: 'Content',
    media: 'Media',
    pinned: 'Pinned',
    blocked: 'Blocked',
    removed: 'Removed',
    likeCount: 'Like Count',
    board: 'Board',
    likers: 'Likers',
    expiresAt: 'Expires At',
    phone: 'Phone',
    level: 'Level',
    description: 'Description',
    
    // Messages
    createSuccess: 'Coupon created successfully',
    updateSuccess: 'Coupon updated successfully',
    deleteSuccess: 'Coupon deleted successfully',
    deleteConfirm: 'Are you sure you want to delete this coupon?',
    
    // Form validation
    titleRequired: 'Coupon title is required',
    boardRequired: 'Board selection is required',
    levelMustBeNumber: 'Level must be a number',
    phoneFormat: 'Please enter a valid phone number',
    expiresAtFormat: 'Please select a valid expiration date',
    
    // Status
    statusActive: 'Active',
    statusExpired: 'Expired',
    statusBlocked: 'Blocked',
    statusRemoved: 'Removed',
  },

  // Search form
  search: {
    boardName: 'Search by board name',
    couponTitle: 'Search by coupon title',
    selectBoard: 'Select board',
    selectType: 'Select type',
    selectLevel: 'Select level',
    dateRange: 'Date range',
    startDate: 'Start date',
    endDate: 'End date',
    expirationRange: 'Expiration range',
  },
}
