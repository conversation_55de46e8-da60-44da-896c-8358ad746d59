export default {
  // Common
  common: {
    search: '搜索',
    reset: '重置',
    create: '创建',
    edit: '编辑',
    delete: '删除',
    save: '保存',
    cancel: '取消',
    confirm: '确认',
    back: '返回',
    actions: '操作',
    status: '状态',
    createdAt: '创建时间',
    updatedAt: '更新时间',
    publishedAt: '发布时间',
  },

  // Benefits Boards
  boards: {
    title: '福利板块',
    name: '板块名称',
    description: '描述',
    type: '类型',
    typePublic: '公开',
    typePrivate: '私有',
    blocked: '已屏蔽',
    isDefault: '默认板块',
    order: '排序',
    postingPermission: '发帖权限',
    userLevelLimited: '用户等级限制',
    related: '关联',
    creator: '创建者',
    subscribers: '订阅者',
    couponsCount: '福利券数量',
    
    // Locales
    locales: '多语言名称',
    localesEn: '英文名称',
    localesKo: '韩文名称',
    
    // Messages
    createSuccess: '板块创建成功',
    updateSuccess: '板块更新成功',
    deleteSuccess: '板块删除成功',
    deleteConfirm: '确定要删除这个板块吗？',
    
    // Form validation
    nameRequired: '板块名称是必填项',
    typeRequired: '板块类型是必填项',
    orderMustBeNumber: '排序必须是数字',
    postingPermissionMustBeNumber: '发帖权限必须是数字',
  },

  // Benefits Coupons
  coupons: {
    title: '福利券',
    couponTitle: '福利券标题',
    content: '内容',
    media: '媒体',
    pinned: '置顶',
    blocked: '已屏蔽',
    removed: '已删除',
    likeCount: '点赞数',
    board: '板块',
    likers: '点赞用户',
    expiresAt: '过期时间',
    phone: '电话',
    level: '等级',
    description: '描述',
    
    // Messages
    createSuccess: '福利券创建成功',
    updateSuccess: '福利券更新成功',
    deleteSuccess: '福利券删除成功',
    deleteConfirm: '确定要删除这个福利券吗？',
    
    // Form validation
    titleRequired: '福利券标题是必填项',
    boardRequired: '必须选择板块',
    levelMustBeNumber: '等级必须是数字',
    phoneFormat: '请输入有效的电话号码',
    expiresAtFormat: '请选择有效的过期时间',
    
    // Status
    statusActive: '活跃',
    statusExpired: '已过期',
    statusBlocked: '已屏蔽',
    statusRemoved: '已删除',
  },

  // Search form
  search: {
    boardName: '按板块名称搜索',
    couponTitle: '按福利券标题搜索',
    selectBoard: '选择板块',
    selectType: '选择类型',
    selectLevel: '选择等级',
    dateRange: '日期范围',
    startDate: '开始日期',
    endDate: '结束日期',
    expirationRange: '过期时间范围',
  },
}
