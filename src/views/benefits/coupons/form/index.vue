<template>
  <PageWrapper :title="getTitle" @back="goBack">
    <div class="benefits-coupon-form-container">
      <a-card class="form-card">
        <div class="form-layout">
          <!-- 左侧基本信息表单 -->
          <div class="form-left">
            <a-form
              :model="formState"
              :rules="rules"
              ref="formRef"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 18 }"
              layout="horizontal"
            >
              <!-- Coupon Title -->
              <a-form-item name="title" :label="t('benefits.coupons.couponTitle')" required>
                <a-input 
                  v-model:value="formState.title" 
                  :placeholder="t('benefits.coupons.couponTitle')" 
                />
              </a-form-item>

              <!-- Board Selection -->
              <a-form-item name="board" :label="t('benefits.coupons.board')" required>
                <a-select 
                  v-model:value="formState.board" 
                  :placeholder="t('benefits.search.selectBoard')"
                  show-search
                  :filter-option="filterOption"
                >
                  <a-select-option 
                    v-for="board in boards" 
                    :key="board.id" 
                    :value="board.id"
                  >
                    {{ board.name }}
                  </a-select-option>
                </a-select>
              </a-form-item>

              <!-- Description -->
              <a-form-item name="description" :label="t('benefits.coupons.description')">
                <a-textarea 
                  v-model:value="formState.description" 
                  :placeholder="t('benefits.coupons.description')"
                  :rows="3"
                />
              </a-form-item>

              <!-- Phone -->
              <a-form-item name="phone" :label="t('benefits.coupons.phone')">
                <a-input 
                  v-model:value="formState.phone" 
                  :placeholder="t('benefits.coupons.phone')" 
                />
              </a-form-item>

              <!-- Level -->
              <a-form-item name="level" :label="t('benefits.coupons.level')">
                <a-input-number 
                  v-model:value="formState.level" 
                  :placeholder="t('benefits.coupons.level')"
                  :min="0"
                  style="width: 100%"
                />
              </a-form-item>

              <!-- Expires At -->
              <a-form-item name="expiresAt" :label="t('benefits.coupons.expiresAt')">
                <a-date-picker 
                  v-model:value="formState.expiresAt" 
                  :placeholder="t('benefits.coupons.expiresAt')"
                  show-time
                  style="width: 100%"
                />
              </a-form-item>

              <!-- Media Upload -->
              <a-form-item name="media" :label="t('benefits.coupons.media')">
                <div class="clearfix">
                  <a-upload
                    v-model:file-list="fileList"
                    accept="image/*,video/*,audio/*"
                    name="files"
                    :headers="headers"
                    list-type="picture-card"
                    :action="uploadUrl"
                    @change="handleChange"
                    @preview="handlePreview"
                  >
                    <div v-if="fileList.length < 8">
                      <plus-outlined />
                      <div style="margin-top: 8px">Upload</div>
                    </div>
                  </a-upload>
                  <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                    <img alt="preview" style="width: 100%" :src="previewImage" />
                  </a-modal>
                </div>
              </a-form-item>

              <!-- Switches -->
              <a-form-item name="pinned" :label="t('benefits.coupons.pinned')">
                <a-switch v-model:checked="formState.pinned" />
              </a-form-item>

              <a-form-item name="blocked" :label="t('benefits.coupons.blocked')">
                <a-switch v-model:checked="formState.blocked" />
              </a-form-item>

              <a-form-item name="removed" :label="t('benefits.coupons.removed')">
                <a-switch v-model:checked="formState.removed" />
              </a-form-item>
            </a-form>
          </div>

          <!-- 右侧Vditor编辑器 -->
          <div class="form-right">
            <a-form-item
              name="content"
              :label="t('benefits.coupons.content')"
              class="content-form-item"
              :label-col="{ span: 24 }"
              :wrapper-col="{ span: 24 }"
            >
              <div id="vditor" class="vditor-container"></div>
            </a-form-item>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <a-space>
            <a-button @click="goBack">{{ t('benefits.common.cancel') }}</a-button>
            <a-button type="primary" @click="handleSubmit" :loading="loading">
              {{ t('benefits.common.save') }}
            </a-button>
          </a-space>
        </div>
      </a-card>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup name="benefitsCouponForm">
  import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { message, UploadChangeParam } from 'ant-design-vue'
  import { PlusOutlined } from '@ant-design/icons-vue'
  import { PageWrapper } from '/@/components/Page'
  import { useI18n } from '/@/hooks/web/useI18n'
  import { useGo } from '/@/hooks/web/usePage'
  import { useGlobSetting } from '/@/hooks/setting'
  import { getToken } from '/@/utils/auth'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import dayjs, { Dayjs } from 'dayjs'
  import Vditor from 'vditor'
  import 'vditor/dist/index.css'
  import {
    getBenefitsCoupon,
    createBenefitsCoupon,
    updateBenefitsCoupon,
    getBenefitsBoards
  } from '/@/api/benefits/benefits'
  import {
    BenefitsCouponCreateParams,
    BenefitsCouponUpdateParams,
    BenefitsBoardModel
  } from '/@/api/benefits/model/benefitsModels'

  const { t } = useI18n()
  const router = useRouter()
  const route = useRoute()
  const go = useGo()

  const formRef = ref()
  const loading = ref(false)
  const isEdit = computed(() => !!route.params.id)
  const couponId = computed(() => route.params.id as string)
  const boards = ref<BenefitsBoardModel[]>([])

  const getTitle = computed(() => {
    return isEdit.value ? t('benefits.coupons.editPage') : t('benefits.coupons.createPage')
  })

  const formState = reactive<BenefitsCouponCreateParams & { expiresAt?: Dayjs }>({
    title: '',
    content: '',
    description: '',
    phone: '',
    level: undefined,
    board: undefined,
    expiresAt: undefined,
    pinned: false,
    blocked: false,
    removed: false,
    media: [],
  })

  const rules = {
    title: [
      { required: true, message: t('benefits.coupons.titleRequired'), trigger: 'blur' },
    ],
    board: [
      { required: true, message: t('benefits.coupons.boardRequired'), trigger: 'change' },
    ],
    level: [
      { type: 'number', message: t('benefits.coupons.levelMustBeNumber'), trigger: 'blur' },
    ],
    phone: [
      { pattern: /^[+]?[\d\s\-()]+$/, message: t('benefits.coupons.phoneFormat'), trigger: 'blur' },
    ],
  }

  // File upload related
  const fileList = ref<any[]>([])
  const previewVisible = ref(false)
  const previewImage = ref('')
  const uploadUrl = useGlobSetting().apiUrl + '/upload'
  const headers = {
    Authorization: `Bearer ${getToken()}`,
  }

  const filterOption = (input: string, option: any) => {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  // Vditor related
  const vditor = ref<Vditor | null>(null)

  const initVditor = () => {
    if (document.getElementById('vditor')) {
      const globSettings = useGlobSetting()

      vditor.value = new Vditor('vditor', {
        height: 600,
        placeholder: 'Please enter coupon content here...',
        mode: 'wysiwyg',
        lang: 'en_US',
        toolbarConfig: {
          pin: true,
        },
        toolbar: [
          'headings',
          'bold',
          'italic',
          'strike',
          'link',
          '|',
          'list',
          'ordered-list',
          'check',
          'outdent',
          'indent',
          '|',
          'quote',
          'line',
          'table',
          'insert-before',
          'insert-after',
          'upload',
          '|',
          'undo',
          'redo',
          '|',
          'both',
        ],
        counter: {
          enable: true,
        },
        cache: {
          enable: false,
        },
        preview: {
          hljs: {
            enable: true,
            style: 'github',
          },
        },
        upload: {
          url: globSettings.apiUrl + '/upload',
          headers: {
            Authorization: `Bearer ${getToken()}`,
          },
          fieldName: 'files',
          max: 10 * 1024 * 1024,
          withCredentials: false,
          accept: 'image/*',
          format(files, responseText) {
            try {
              const res = JSON.parse(responseText)
              const file = res[0]

              if (file && file.url) {
                return JSON.stringify({
                  msg: '',
                  code: 0,
                  data: {
                    errFiles: [],
                    succMap: {
                      [files[0].name]: file.url,
                    },
                  },
                })
              } else {
                return JSON.stringify({
                  msg: 'Upload failed',
                  code: -1,
                })
              }
            } catch (e: any) {
              console.error('Failed to parse upload response:', e)
              return JSON.stringify({
                msg: 'Upload failed: ' + e.message,
                code: -1,
              })
            }
          },
          success(_editor: any, msg: any) {
            try {
              let fileData: any
              if (typeof msg === 'string') {
                try {
                  fileData = JSON.parse(msg)
                } catch (err) {
                  fileData = msg
                }
              } else {
                fileData = msg
              }

              if (fileData?.data?.succMap) {
                const imgUrl = Object.values(fileData.data.succMap)[0]
                if (imgUrl) {
                  vditor?.value?.insertValue(`![image](${imgUrl})`)
                  message.success('Image uploaded successfully')
                }
              }
            } catch (error) {
              console.error('Upload success handler error:', error)
              message.error('Failed to process uploaded image')
            }
          },
        },
        after: () => {
          if (formState.content) {
            vditor.value?.setValue(formState.content)
          }
        },
      })
    }
  }

  // File upload handlers
  const getBase64 = (file: File): Promise<string> =>
    new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = (error) => reject(error)
    })

  const handlePreview = async (file: any) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj)
    }
    previewImage.value = file.url || file.preview
    previewVisible.value = true
  }

  const handleCancel = () => {
    previewVisible.value = false
  }

  const handleChange = (info: UploadChangeParam) => {
    if (info.file.status === 'done') {
      const id = info.file.response[0].id
      const uid = info.file.originFileObj?.uid
      const currentFile = info.fileList.find((file) => file.uid === uid)
      if (currentFile) {
        currentFile.uid = String(id)
      }
      message.success(`${info.file.name} file uploaded successfully`)

      // Update media IDs for form submission
      formState.media = info.fileList
        .filter((file) => file.status === 'done')
        .map((file) => Number(file.uid))
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} file upload failed`)
    }
  }

  const fetchBoards = async () => {
    try {
      const response = await getBenefitsBoards({ page: 1, pageSize: 100 })
      if (response) {
        boards.value = response.data || []
      }
    } catch (error) {
      console.error('Failed to fetch boards:', error)
    }
  }

  const loadCouponData = async () => {
    if (!isEdit.value) return

    try {
      loading.value = true
      const response = await getBenefitsCoupon(Number(couponId.value))

      if (response) {
        Object.assign(formState, {
          title: response.title || '',
          description: response.description || '',
          phone: response.phone || '',
          level: response.level || undefined,
          board: response.board?.id || undefined,
          expiresAt: response.expiresAt ? dayjs(response.expiresAt) : undefined,
          pinned: response.pinned || false,
          blocked: response.blocked || false,
          removed: response.removed || false,
        })

        // Set content for Vditor
        if (response.content) {
          formState.content = response.content
          if (vditor.value) {
            vditor.value.setValue(response.content)
          }
        }

        // Set media files
        if (response.media && response.media.length > 0) {
          fileList.value = response.media.map((item, index) => ({
            uid: String(item.id),
            name: `media-${index}`,
            status: 'done',
            url: item.url,
            response: [item],
          }))
          formState.media = response.media.map(item => item.id)
        }
      }
    } catch (error) {
      console.error('Failed to load coupon data:', error)
      message.error('Failed to load coupon data')
    } finally {
      loading.value = false
    }
  }

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate()
      loading.value = true

      // Get content from Vditor
      const content = vditor.value?.getValue() || ''

      const submitData = {
        ...formState,
        content,
        expiresAt: formState.expiresAt ? formState.expiresAt.toISOString() : undefined,
      }

      if (isEdit.value) {
        const updateData: BenefitsCouponUpdateParams = {
          ...submitData,
          id: Number(couponId.value),
        }
        await updateBenefitsCoupon(Number(couponId.value), updateData)
        message.success(t('benefits.coupons.updateSuccess'))
      } else {
        await createBenefitsCoupon(submitData)
        message.success(t('benefits.coupons.createSuccess'))
      }

      goBack()
    } catch (error) {
      console.error('Failed to submit form:', error)
      if (error?.errorFields) {
        // Form validation errors
        return
      }
      message.error('Failed to save coupon')
    } finally {
      loading.value = false
    }
  }

  const goBack = () => {
    go('/benefits/coupons')
  }

  onMounted(async () => {
    await fetchBoards()

    // Initialize Vditor after DOM is ready
    await nextTick()
    initVditor()

    if (isEdit.value) {
      await loadCouponData()
    }
  })

  onUnmounted(() => {
    if (vditor.value) {
      vditor.value.destroy()
    }
  })
</script>

<style lang="less" scoped>
  .benefits-coupon-form-container {

    // Responsive design
    @media (max-width: 1200px) {
      .form-layout {
        flex-direction: column;

        .form-left,
        .form-right {
          min-width: auto;
        }
      }
    }

    .form-card {
      margin: 0 auto;
    }

    .form-layout {
      display: flex;
      gap: 24px;

      .form-left {
        flex: 1;
        min-width: 400px;

        .ant-form-item {
          margin-bottom: 24px;
        }
      }

      .form-right {
        flex: 1;
        min-width: 500px;

        .content-form-item {
          .ant-form-item-label {
            text-align: left;
            margin-bottom: 12px;
          }
        }

        .vditor-container {
          border: 1px solid #d9d9d9;
          border-radius: 6px;
          overflow: hidden;
        }
      }
    }

    .form-actions {
      margin-top: 24px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
      text-align: center;
    }

    // Media upload styles
    .clearfix {
      .ant-upload-select-picture-card {
        width: 104px;
        height: 104px;
      }
    }
  }

  // Vditor global styles
  :global(.vditor) {
    border: none !important;
  }

  :global(.vditor-toolbar) {
    border-bottom: 1px solid #e8e8e8 !important;
  }

  :global(.vditor-content) {
    background-color: #fff !important;
  }
</style>
