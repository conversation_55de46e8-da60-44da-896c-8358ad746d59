<template>
  <PageWrapper :title="t('benefits.coupons.title')">
    <div class="benefits-coupons-container">
      <!-- Search Form -->
      <div class="search-form-container mb-4">
        <a-card :bordered="false">
          <a-form
            :model="searchParams"
            layout="inline"
            class="search-form"
            @finish="handleSearch"
          >
            <a-form-item :label="t('benefits.coupons.couponTitle')">
              <a-input
                v-model:value="searchParams.title"
                :placeholder="t('benefits.search.couponTitle')"
                allow-clear
                style="width: 200px"
              />
            </a-form-item>
            
            <a-form-item :label="t('benefits.coupons.board')">
              <a-select
                v-model:value="searchParams.boardId"
                :placeholder="t('benefits.search.selectBoard')"
                allow-clear
                style="width: 200px"
                show-search
                :filter-option="filterOption"
              >
                <a-select-option 
                  v-for="board in boards" 
                  :key="board.id" 
                  :value="board.id"
                >
                  {{ board.name }}
                </a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item :label="t('benefits.coupons.level')">
              <a-input-number
                v-model:value="searchParams.level"
                :placeholder="t('benefits.search.selectLevel')"
                style="width: 120px"
                :min="0"
              />
            </a-form-item>
            
            <a-form-item :label="t('benefits.coupons.pinned')">
              <a-select
                v-model:value="searchParams.pinned"
                :placeholder="t('benefits.common.status')"
                allow-clear
                style="width: 120px"
              >
                <a-select-option :value="true">{{ t('benefits.coupons.pinned') }}</a-select-option>
                <a-select-option :value="false">Normal</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item :label="t('benefits.coupons.blocked')">
              <a-select
                v-model:value="searchParams.blocked"
                :placeholder="t('benefits.common.status')"
                allow-clear
                style="width: 120px"
              >
                <a-select-option :value="true">{{ t('benefits.coupons.blocked') }}</a-select-option>
                <a-select-option :value="false">{{ t('benefits.coupons.statusActive') }}</a-select-option>
              </a-select>
            </a-form-item>
            
            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  {{ t('benefits.common.search') }}
                </a-button>
                <a-button @click="handleReset">
                  {{ t('benefits.common.reset') }}
                </a-button>
                <a-button type="primary" @click="handleCreate">
                  {{ t('benefits.common.create') }}
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </div>

      <!-- Table -->
      <div class="table-container">
        <a-table
          :columns="columns"
          :dataSource="dataSource"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          bordered
          size="small"
          :scroll="{ x: 1400 }"
          :rowKey="(record) => record.id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'title'">
              <a-button type="link" @click="handleView(record)">
                {{ record.title }}
              </a-button>
            </template>
            
            <template v-if="column.key === 'content'">
              <div class="content-preview" v-html="record.content?.substring(0, 100) + '...'" />
            </template>
            
            <template v-if="column.key === 'media'">
              <div v-if="record.media && record.media.length > 0" class="media-preview">
                <a-image
                  v-for="(item, index) in record.media.slice(0, 3)"
                  :key="index"
                  :width="30"
                  :height="30"
                  :src="item.formats?.thumbnail?.url || item.url"
                  style="margin-right: 4px"
                />
                <span v-if="record.media.length > 3">+{{ record.media.length - 3 }}</span>
              </div>
            </template>
            
            <template v-if="column.key === 'board'">
              <a-tag color="blue">{{ record.board?.name }}</a-tag>
            </template>
            
            <template v-if="column.key === 'pinned'">
              <a-tag v-if="record.pinned" color="red">
                {{ t('benefits.coupons.pinned') }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'blocked'">
              <a-tag :color="record.blocked ? 'red' : 'green'">
                {{ record.blocked ? t('benefits.coupons.blocked') : t('benefits.coupons.statusActive') }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'status'">
              <a-tag :color="getStatusColor(record)">
                {{ getStatusText(record) }}
              </a-tag>
            </template>
            
            <template v-if="column.key === 'expiresAt'">
              {{ record.expiresAt ? formatToDateTime(record.expiresAt) : '-' }}
            </template>
            
            <template v-if="column.key === 'createdAt'">
              {{ formatToDateTime(record.createdAt) }}
            </template>
            
            <template v-if="column.key === 'action'">
              <a-space>
                <a-tooltip :title="t('benefits.common.edit')">
                  <a-button type="link" @click="handleEdit(record)">
                    <Icon icon="ant-design:edit-outlined" />
                  </a-button>
                </a-tooltip>
                <a-popconfirm
                  :title="t('benefits.coupons.deleteConfirm')"
                  placement="left"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <Icon icon="ant-design:delete-outlined" />
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup name="benefitsCouponsList">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { message } from 'ant-design-vue'
  import { PageWrapper } from '/@/components/Page'
  import { Icon } from '/@/components/Icon'
  import { useI18n } from '/@/hooks/web/useI18n'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import {
    getBenefitsCoupons,
    deleteBenefitsCoupon
  } from '/@/api/benefits/benefits'
  import { getBenefitsBoards } from '/@/api/benefits/benefits'
  import {
    BenefitsCouponModel,
    BenefitsCouponSearchParams,
    BenefitsBoardModel
  } from '/@/api/benefits/model/benefitsModels'

  const { t } = useI18n()
  const router = useRouter()

  const loading = ref(false)
  const dataSource = ref<BenefitsCouponModel[]>([])
  const boards = ref<BenefitsBoardModel[]>([])

  const searchParams = reactive<BenefitsCouponSearchParams>({
    page: 1,
    pageSize: 10,
    title: '',
    boardId: undefined,
    level: undefined,
    pinned: undefined,
    blocked: undefined,
  })

  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `Total ${total} items`,
  })

  const columns = computed(() => [
    {
      title: t('benefits.coupons.couponTitle'),
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: t('benefits.coupons.content'),
      dataIndex: 'content',
      key: 'content',
      width: 250,
      ellipsis: true,
    },
    {
      title: t('benefits.coupons.media'),
      dataIndex: 'media',
      key: 'media',
      width: 120,
    },
    {
      title: t('benefits.coupons.board'),
      dataIndex: 'board',
      key: 'board',
      width: 150,
    },
    {
      title: t('benefits.coupons.level'),
      dataIndex: 'level',
      key: 'level',
      width: 80,
    },
    {
      title: t('benefits.coupons.pinned'),
      dataIndex: 'pinned',
      key: 'pinned',
      width: 100,
    },
    {
      title: t('benefits.common.status'),
      key: 'status',
      width: 120,
    },
    {
      title: t('benefits.coupons.likeCount'),
      dataIndex: 'likeCount',
      key: 'likeCount',
      width: 100,
    },
    {
      title: t('benefits.coupons.expiresAt'),
      dataIndex: 'expiresAt',
      key: 'expiresAt',
      width: 180,
    },
    {
      title: t('benefits.common.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
    },
    {
      title: t('benefits.common.actions'),
      key: 'action',
      width: 120,
      fixed: 'right',
    },
  ])

  const filterOption = (input: string, option: any) => {
    return option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
  }

  const getStatusColor = (record: BenefitsCouponModel) => {
    if (record.removed) return 'red'
    if (record.blocked) return 'orange'
    if (record.expiresAt && new Date(record.expiresAt) < new Date()) return 'default'
    return 'green'
  }

  const getStatusText = (record: BenefitsCouponModel) => {
    if (record.removed) return t('benefits.coupons.statusRemoved')
    if (record.blocked) return t('benefits.coupons.statusBlocked')
    if (record.expiresAt && new Date(record.expiresAt) < new Date()) return t('benefits.coupons.statusExpired')
    return t('benefits.coupons.statusActive')
  }

  const fetchBoards = async () => {
    try {
      const response = await getBenefitsBoards({ page: 1, pageSize: 100 })
      if (response) {
        boards.value = response.data || []
      }
    } catch (error) {
      console.error('Failed to fetch boards:', error)
    }
  }

  const fetchData = async () => {
    try {
      loading.value = true
      const response = await getBenefitsCoupons(searchParams)

      if (response) {
        dataSource.value = response.data || []
        pagination.value.total = response.meta?.pagination?.total || 0
        pagination.value.current = response.meta?.pagination?.page || 1
      }
    } catch (error) {
      console.error('Failed to fetch benefits coupons:', error)
      message.error('Failed to load benefits coupons')
    } finally {
      loading.value = false
    }
  }

  const handleSearch = () => {
    searchParams.page = 1
    pagination.value.current = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchParams, {
      page: 1,
      pageSize: 10,
      title: '',
      boardId: undefined,
      level: undefined,
      pinned: undefined,
      blocked: undefined,
    })
    pagination.value.current = 1
    fetchData()
  }

  const handleTableChange = (pag: any) => {
    pagination.value.current = pag.current
    pagination.value.pageSize = pag.pageSize
    searchParams.page = pag.current
    searchParams.pageSize = pag.pageSize
    fetchData()
  }

  const handleCreate = () => {
    router.push('/benefits/coupons/create')
  }

  const handleEdit = (record: BenefitsCouponModel) => {
    router.push(`/benefits/coupons/edit/${record.id}`)
  }

  const handleView = (record: BenefitsCouponModel) => {
    // Could implement a detail modal or navigate to detail page
    console.log('View coupon:', record)
  }

  const handleDelete = async (record: BenefitsCouponModel) => {
    try {
      await deleteBenefitsCoupon(record.id)
      message.success(t('benefits.coupons.deleteSuccess'))
      fetchData()
    } catch (error) {
      console.error('Failed to delete coupon:', error)
      message.error('Failed to delete coupon')
    }
  }

  onMounted(() => {
    fetchBoards()
    fetchData()
  })
</script>

<style lang="less" scoped>
  .benefits-coupons-container {
    .search-form-container {
      .search-form {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }

    .table-container {
      background: #fff;
      border-radius: 6px;
    }

    .content-preview {
      max-width: 250px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .media-preview {
      display: flex;
      align-items: center;

      .ant-image {
        border-radius: 4px;
        overflow: hidden;
      }
    }
  }
</style>
