<template>
  <PageWrapper :title="t('benefits.boards.title')">
    <div class="benefits-boards-container">
      <!-- Search Form -->
      <div class="search-form-container mb-4">
        <a-card :bordered="false">
          <a-form :model="searchParams" layout="inline" class="search-form" @finish="handleSearch">
            <a-form-item :label="t('benefits.boards.name')">
              <a-input
                v-model:value="searchParams.name"
                :placeholder="t('benefits.search.boardName')"
                allow-clear
                style="width: 200px"
              />
            </a-form-item>

            <a-form-item :label="t('benefits.boards.type')">
              <a-select
                v-model:value="searchParams.type"
                :placeholder="t('benefits.search.selectType')"
                allow-clear
                style="width: 120px"
              >
                <a-select-option value="public">{{
                  t('benefits.boards.typePublic')
                }}</a-select-option>
                <a-select-option value="private">{{
                  t('benefits.boards.typePrivate')
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item :label="t('benefits.boards.blocked')">
              <a-select
                v-model:value="searchParams.blocked"
                :placeholder="t('benefits.common.status')"
                allow-clear
                style="width: 120px"
              >
                <a-select-option :value="true">{{ t('benefits.boards.blocked') }}</a-select-option>
                <a-select-option :value="false">{{
                  t('benefits.boards.typePublic')
                }}</a-select-option>
              </a-select>
            </a-form-item>

            <a-form-item>
              <a-space>
                <a-button type="primary" html-type="submit" :loading="loading">
                  {{ t('benefits.common.search') }}
                </a-button>
                <a-button @click="handleReset">
                  {{ t('benefits.common.reset') }}
                </a-button>
                <a-button type="primary" @click="handleCreate">
                  {{ t('benefits.common.create') }}
                </a-button>
              </a-space>
            </a-form-item>
          </a-form>
        </a-card>
      </div>

      <!-- Table -->
      <div class="table-container">
        <a-table
          :columns="columns"
          :dataSource="dataSource"
          :loading="loading"
          :pagination="pagination"
          @change="handleTableChange"
          bordered
          size="small"
          :scroll="{ x: 1200 }"
          :rowKey="(record) => record.id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'name'">
              <a-button type="link" @click="handleView(record)">
                {{ record.name }}
              </a-button>
            </template>

            <template v-if="column.key === 'type'">
              <a-tag :color="record.type === 'public' ? 'green' : 'orange'">
                {{
                  record.type === 'public'
                    ? t('benefits.boards.typePublic')
                    : t('benefits.boards.typePrivate')
                }}
              </a-tag>
            </template>

            <template v-if="column.key === 'blocked'">
              <a-tag :color="record.blocked ? 'red' : 'green'">
                {{
                  record.blocked ? t('benefits.boards.blocked') : t('benefits.boards.typePublic')
                }}
              </a-tag>
            </template>

            <template v-if="column.key === 'isDefault'">
              <a-tag v-if="record.isDefault" color="blue">
                {{ t('benefits.boards.isDefault') }}
              </a-tag>
            </template>

            <template v-if="column.key === 'couponsCount'">
              {{ record.coupons?.length || 0 }}
            </template>

            <template v-if="column.key === 'createdAt'">
              {{ formatToDateTime(record.createdAt) }}
            </template>

            <template v-if="column.key === 'action'">
              <a-space>
                <a-tooltip :title="t('benefits.common.edit')">
                  <a-button type="link" @click="handleEdit(record)">
                    <Icon icon="ant-design:edit-outlined" />
                  </a-button>
                </a-tooltip>
                <a-popconfirm
                  :title="t('benefits.boards.deleteConfirm')"
                  placement="left"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" danger>
                    <Icon icon="ant-design:delete-outlined" />
                  </a-button>
                </a-popconfirm>
              </a-space>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup name="benefitsBoardsList">
  import { ref, reactive, onMounted, computed } from 'vue'
  import { useRouter } from 'vue-router'
  import { message } from 'ant-design-vue'
  import { PageWrapper } from '/@/components/Page'
  import { Icon } from '/@/components/Icon'
  import { useI18n } from '/@/hooks/web/useI18n'
  import { formatToDateTime } from '/@/utils/dateUtil'
  import { getBenefitsBoards, deleteBenefitsBoard } from '/@/api/benefits/benefits'
  import {
    BenefitsBoardModel,
    BenefitsBoardSearchParams,
  } from '/@/api/benefits/model/benefitsModels'

  const { t } = useI18n()
  const router = useRouter()

  const loading = ref(false)
  const dataSource = ref<BenefitsBoardModel[]>([])

  const searchParams = reactive<BenefitsBoardSearchParams>({
    page: 1,
    pageSize: 10,
    name: '',
    type: undefined,
    blocked: undefined,
  })

  const pagination = ref({
    current: 1,
    total: 0,
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total: number) => `Total ${total} items`,
  })

  const columns = computed(() => [
    {
      title: t('benefits.boards.name'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true,
    },
    {
      title: t('benefits.boards.description'),
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true,
    },
    {
      title: t('benefits.boards.type'),
      dataIndex: 'type',
      key: 'type',
      width: 100,
    },
    {
      title: t('benefits.boards.blocked'),
      dataIndex: 'blocked',
      key: 'blocked',
      width: 100,
    },
    {
      title: t('benefits.boards.isDefault'),
      dataIndex: 'isDefault',
      key: 'isDefault',
      width: 120,
    },
    {
      title: t('benefits.boards.order'),
      dataIndex: 'order',
      key: 'order',
      width: 80,
    },
    {
      title: t('benefits.boards.couponsCount'),
      dataIndex: 'couponsCount',
      key: 'couponsCount',
      width: 120,
    },
    {
      title: t('benefits.common.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
    },
    {
      title: t('benefits.common.actions'),
      key: 'action',
      width: 120,
      fixed: 'right',
    },
  ])

  const fetchData = async () => {
    try {
      loading.value = true
      const response = await getBenefitsBoards(searchParams)

      if (response) {
        dataSource.value = response.results || []
        pagination.value.total = response.pagination?.total || 0
        pagination.value.current = response.pagination?.page || 1
      }
    } catch (error) {
      console.error('Failed to fetch benefits boards:', error)
      message.error('Failed to load benefits boards')
    } finally {
      loading.value = false
    }
  }

  const handleSearch = () => {
    searchParams.page = 1
    pagination.value.current = 1
    fetchData()
  }

  const handleReset = () => {
    Object.assign(searchParams, {
      page: 1,
      pageSize: 10,
      name: '',
      type: undefined,
      blocked: undefined,
    })
    pagination.value.current = 1
    fetchData()
  }

  const handleTableChange = (pag: any) => {
    pagination.value.current = pag.current
    pagination.value.pageSize = pag.pageSize
    searchParams.page = pag.current
    searchParams.pageSize = pag.pageSize
    fetchData()
  }

  const handleCreate = () => {
    router.push('/benefits/boards/create')
  }

  const handleEdit = (record: BenefitsBoardModel) => {
    router.push(`/benefits/boards/edit/${record.id}`)
  }

  const handleView = (record: BenefitsBoardModel) => {
    // Could implement a detail modal or navigate to detail page
    console.log('View board:', record)
  }

  const handleDelete = async (record: BenefitsBoardModel) => {
    try {
      await deleteBenefitsBoard(record.id)
      message.success(t('benefits.boards.deleteSuccess'))
      fetchData()
    } catch (error) {
      console.error('Failed to delete board:', error)
      message.error('Failed to delete board')
    }
  }

  onMounted(() => {
    fetchData()
  })
</script>

<style lang="less" scoped>
  .benefits-boards-container {
    .search-form-container {
      .search-form {
        .ant-form-item {
          margin-bottom: 0;
        }
      }
    }

    .table-container {
      background: #fff;
      border-radius: 6px;
    }
  }
</style>
