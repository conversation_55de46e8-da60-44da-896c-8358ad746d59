<template>
  <PageWrapper :title="getTitle" @back="goBack">
    <div class="benefits-board-form-container">
      <a-card class="form-card">
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
          layout="horizontal"
        >
          <!-- Board Name -->
          <a-form-item name="name" :label="t('benefits.boards.name')" required>
            <a-input v-model:value="formState.name" :placeholder="t('benefits.boards.name')" />
          </a-form-item>

          <!-- Localized Names -->
          <a-form-item :label="t('benefits.boards.locales')">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item name="locales.en" :label="t('benefits.boards.localesEn')">
                  <a-input
                    v-model:value="formState.locales.en"
                    :placeholder="t('benefits.boards.localesEn')"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="locales.ko" :label="t('benefits.boards.localesKo')">
                  <a-input
                    v-model:value="formState.locales.ko"
                    :placeholder="t('benefits.boards.localesKo')"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item name="locales.zh_CN" :label="t('benefits.boards.localesZhCN')">
                  <a-input
                    v-model:value="formState.locales.zh_CN"
                    :placeholder="t('benefits.boards.localesZhCN')"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form-item>

          <!-- Description -->
          <a-form-item name="description" :label="t('benefits.boards.description')">
            <a-textarea
              v-model:value="formState.description"
              :placeholder="t('benefits.boards.description')"
              :rows="3"
            />
          </a-form-item>

          <!-- Type -->
          <a-form-item name="type" :label="t('benefits.boards.type')" required>
            <a-select v-model:value="formState.type" :placeholder="t('benefits.search.selectType')">
              <a-select-option value="public">{{
                t('benefits.boards.typePublic')
              }}</a-select-option>
              <a-select-option value="private">{{
                t('benefits.boards.typePrivate')
              }}</a-select-option>
            </a-select>
          </a-form-item>

          <!-- User Level Limited -->
          <a-form-item name="userLevelLimited" :label="t('benefits.boards.userLevelLimited')">
            <a-input
              v-model:value="formState.userLevelLimited"
              :placeholder="t('benefits.boards.userLevelLimited')"
            />
          </a-form-item>

          <!-- Blocked -->
          <a-form-item name="blocked" :label="t('benefits.boards.blocked')">
            <a-switch v-model:checked="formState.blocked" />
          </a-form-item>

          <!-- Form Actions -->
          <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
            <a-space>
              <a-button type="primary" @click="handleSubmit" :loading="loading">
                {{ t('benefits.common.save') }}
              </a-button>
              <a-button @click="goBack">
                {{ t('benefits.common.cancel') }}
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup name="benefitsBoardForm">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { message } from 'ant-design-vue'
  import { PageWrapper } from '/@/components/Page'
  import { useI18n } from '/@/hooks/web/useI18n'
  import { useGo } from '/@/hooks/web/usePage'
  import {
    getBenefitsBoard,
    createBenefitsBoard,
    updateBenefitsBoard,
  } from '/@/api/benefits/benefits'
  import {
    BenefitsBoardCreateParams,
    BenefitsBoardUpdateParams,
  } from '/@/api/benefits/model/benefitsModels'

  const { t } = useI18n()
  const route = useRoute()
  const go = useGo()

  const formRef = ref()
  const loading = ref(false)
  const isEdit = computed(() => !!route.params.id)
  const boardId = computed(() => route.params.id as string)

  const getTitle = computed(() => {
    return isEdit.value
      ? t('routes.benefits.boards.editPage')
      : t('routes.benefits.boards.createPage')
  })

  const formState = reactive<BenefitsBoardCreateParams>({
    name: '',
    locales: {
      en: '',
      ko: '',
      zh_CN: '',
    },
    description: '',
    type: 'public',
    userLevelLimited: '',
    blocked: false,
  })

  const rules = {
    name: [{ required: true, message: t('benefits.boards.nameRequired'), trigger: 'blur' }],
    type: [{ required: true, message: t('benefits.boards.typeRequired'), trigger: 'change' }],
  }

  const loadBoardData = async () => {
    if (!isEdit.value) return

    try {
      loading.value = true
      const response = await getBenefitsBoard(Number(boardId.value))

      if (response) {
        Object.assign(formState, {
          name: response.name || '',
          locales: {
            en: response.locales?.en || '',
            ko: response.locales?.ko || '',
            zh_CN: response.locales?.zh_CN || '',
          },
          description: response.description || '',
          type: response.type || 'public',
          userLevelLimited: response.userLevelLimited || '',
          blocked: response.blocked || false,
        })
      }
    } catch (error) {
      console.error('Failed to load board data:', error)
      message.error('Failed to load board data')
    } finally {
      loading.value = false
    }
  }

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate()
      loading.value = true

      const submitData = {
        name: formState.name,
        locales: Object.keys(formState.locales || {}).reduce((acc, key) => {
          if (formState.locales?.[key]) {
            acc[key] = formState.locales[key]
          }
          return acc
        }, {} as any),
        description: formState.description,
        blocked: formState.blocked,
        type: formState.type,
        userLevelLimited: formState.userLevelLimited,
      }

      if (isEdit.value) {
        const updateData: BenefitsBoardUpdateParams = {
          ...submitData,
          id: Number(boardId.value),
        }
        await updateBenefitsBoard(Number(boardId.value), updateData)
        message.success(t('benefits.boards.updateSuccess'))
      } else {
        await createBenefitsBoard(submitData)
        message.success(t('benefits.boards.createSuccess'))
      }

      goBack()
    } catch (error) {
      console.error('Failed to submit form:', error)
      if (error?.errorFields) {
        // Form validation errors
        return
      }
      message.error('Failed to save board')
    } finally {
      loading.value = false
    }
  }

  const goBack = () => {
    go('/benefits/boards')
  }

  onMounted(() => {
    if (isEdit.value) {
      loadBoardData()
    }
  })
</script>

<style lang="less" scoped>
  .benefits-board-form-container {
    .form-card {
      max-width: 800px;
      margin: 0 auto;
    }

    .ant-form-item {
      margin-bottom: 24px;
    }
  }
</style>
