<template>
  <PageWrapper :title="getTitle" @back="goBack">
    <div class="benefits-board-form-container">
      <a-card class="form-card">
        <a-form
          :model="formState"
          :rules="rules"
          ref="formRef"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 18 }"
          layout="horizontal"
        >
          <!-- Board Name -->
          <a-form-item name="name" :label="t('benefits.boards.name')" required>
            <a-input 
              v-model:value="formState.name" 
              :placeholder="t('benefits.boards.name')" 
            />
          </a-form-item>

          <!-- Localized Names -->
          <a-form-item :label="t('benefits.boards.locales')">
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item name="locales.en" :label="t('benefits.boards.localesEn')">
                  <a-input 
                    v-model:value="formState.locales.en" 
                    :placeholder="t('benefits.boards.localesEn')" 
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item name="locales.ko" :label="t('benefits.boards.localesKo')">
                  <a-input 
                    v-model:value="formState.locales.ko" 
                    :placeholder="t('benefits.boards.localesKo')" 
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form-item>

          <!-- Description -->
          <a-form-item name="description" :label="t('benefits.boards.description')">
            <a-textarea 
              v-model:value="formState.description" 
              :placeholder="t('benefits.boards.description')"
              :rows="3"
            />
          </a-form-item>

          <!-- Type -->
          <a-form-item name="type" :label="t('benefits.boards.type')" required>
            <a-select 
              v-model:value="formState.type" 
              :placeholder="t('benefits.search.selectType')"
            >
              <a-select-option value="public">{{ t('benefits.boards.typePublic') }}</a-select-option>
              <a-select-option value="private">{{ t('benefits.boards.typePrivate') }}</a-select-option>
            </a-select>
          </a-form-item>

          <!-- Related -->
          <a-form-item name="related" :label="t('benefits.boards.related')">
            <a-input 
              v-model:value="formState.related" 
              :placeholder="t('benefits.boards.related')" 
            />
          </a-form-item>

          <!-- Order -->
          <a-form-item name="order" :label="t('benefits.boards.order')">
            <a-input-number 
              v-model:value="formState.order" 
              :placeholder="t('benefits.boards.order')"
              :min="0"
              style="width: 100%"
            />
          </a-form-item>

          <!-- Posting Permission -->
          <a-form-item name="postingPermission" :label="t('benefits.boards.postingPermission')">
            <a-input-number 
              v-model:value="formState.postingPermission" 
              :placeholder="t('benefits.boards.postingPermission')"
              :min="0"
              style="width: 100%"
            />
          </a-form-item>

          <!-- User Level Limited -->
          <a-form-item name="userLevelLimited" :label="t('benefits.boards.userLevelLimited')">
            <a-input 
              v-model:value="formState.userLevelLimited" 
              :placeholder="t('benefits.boards.userLevelLimited')" 
            />
          </a-form-item>

          <!-- Switches -->
          <a-form-item name="blocked" :label="t('benefits.boards.blocked')">
            <a-switch v-model:checked="formState.blocked" />
          </a-form-item>

          <a-form-item name="isDefault" :label="t('benefits.boards.isDefault')">
            <a-switch v-model:checked="formState.isDefault" />
          </a-form-item>

          <!-- Form Actions -->
          <a-form-item :wrapper-col="{ offset: 6, span: 18 }">
            <a-space>
              <a-button type="primary" @click="handleSubmit" :loading="loading">
                {{ t('benefits.common.save') }}
              </a-button>
              <a-button @click="goBack">
                {{ t('benefits.common.cancel') }}
              </a-button>
            </a-space>
          </a-form-item>
        </a-form>
      </a-card>
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup name="benefitsBoardForm">
  import { ref, reactive, computed, onMounted } from 'vue'
  import { useRouter, useRoute } from 'vue-router'
  import { message } from 'ant-design-vue'
  import { PageWrapper } from '/@/components/Page'
  import { useI18n } from '/@/hooks/web/useI18n'
  import { useGo } from '/@/hooks/web/usePage'
  import {
    getBenefitsBoard,
    createBenefitsBoard,
    updateBenefitsBoard
  } from '/@/api/benefits/benefits'
  import {
    BenefitsBoardCreateParams,
    BenefitsBoardUpdateParams
  } from '/@/api/benefits/model/benefitsModels'

  const { t } = useI18n()
  const router = useRouter()
  const route = useRoute()
  const go = useGo()

  const formRef = ref()
  const loading = ref(false)
  const isEdit = computed(() => !!route.params.id)
  const boardId = computed(() => route.params.id as string)

  const getTitle = computed(() => {
    return isEdit.value ? t('benefits.boards.editPage') : t('benefits.boards.createPage')
  })

  const formState = reactive<BenefitsBoardCreateParams>({
    name: '',
    locales: {
      en: '',
      ko: '',
    },
    description: '',
    type: 'public',
    related: '',
    order: 0,
    postingPermission: 0,
    userLevelLimited: '',
    blocked: false,
    isDefault: false,
  })

  const rules = {
    name: [
      { required: true, message: t('benefits.boards.nameRequired'), trigger: 'blur' },
    ],
    type: [
      { required: true, message: t('benefits.boards.typeRequired'), trigger: 'change' },
    ],
    order: [
      { type: 'number', message: t('benefits.boards.orderMustBeNumber'), trigger: 'blur' },
    ],
    postingPermission: [
      { type: 'number', message: t('benefits.boards.postingPermissionMustBeNumber'), trigger: 'blur' },
    ],
  }

  const loadBoardData = async () => {
    if (!isEdit.value) return

    try {
      loading.value = true
      const response = await getBenefitsBoard(Number(boardId.value))

      if (response) {
        Object.assign(formState, {
          name: response.name || '',
          locales: {
            en: response.locales?.en || '',
            ko: response.locales?.ko || '',
          },
          description: response.description || '',
          type: response.type || 'public',
          related: response.related || '',
          order: response.order || 0,
          postingPermission: response.postingPermission || 0,
          userLevelLimited: response.userLevelLimited || '',
          blocked: response.blocked || false,
          isDefault: response.isDefault || false,
        })
      }
    } catch (error) {
      console.error('Failed to load board data:', error)
      message.error('Failed to load board data')
    } finally {
      loading.value = false
    }
  }

  const handleSubmit = async () => {
    try {
      await formRef.value?.validate()
      loading.value = true

      const submitData = {
        ...formState,
        // Clean up empty locales
        locales: Object.keys(formState.locales || {}).reduce((acc, key) => {
          if (formState.locales?.[key]) {
            acc[key] = formState.locales[key]
          }
          return acc
        }, {} as any),
      }

      if (isEdit.value) {
        const updateData: BenefitsBoardUpdateParams = {
          ...submitData,
          id: Number(boardId.value),
        }
        await updateBenefitsBoard(Number(boardId.value), updateData)
        message.success(t('benefits.boards.updateSuccess'))
      } else {
        await createBenefitsBoard(submitData)
        message.success(t('benefits.boards.createSuccess'))
      }

      goBack()
    } catch (error) {
      console.error('Failed to submit form:', error)
      if (error?.errorFields) {
        // Form validation errors
        return
      }
      message.error('Failed to save board')
    } finally {
      loading.value = false
    }
  }

  const goBack = () => {
    go('/benefits/boards')
  }

  onMounted(() => {
    if (isEdit.value) {
      loadBoardData()
    }
  })
</script>

<style lang="less" scoped>
  .benefits-board-form-container {
    .form-card {
      max-width: 800px;
      margin: 0 auto;
    }

    .ant-form-item {
      margin-bottom: 24px;
    }
  }
</style>
